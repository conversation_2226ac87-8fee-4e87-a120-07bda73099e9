<?php

namespace Sparefoot\MyFootService\EventSubscriber;

use S<PERSON>fony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\HttpKernel\Event\ControllerEvent;
use Symfony\Component\HttpKernel\KernelEvents;

class ControllerInitSubscriber implements EventSubscriberInterface
{
    public static function getSubscribedEvents(): array
    {
        return [
            KernelEvents::CONTROLLER => 'onKernelController',
        ];
    }

    public function onKernelController(ControllerEvent $event): void
    {
        $controller = $event->getController();

        // Handle both array callables and single object callables
        if (is_array($controller)) {
            $controllerObject = $controller[0];
        } elseif (is_object($controller) && method_exists($controller, '__invoke')) {
            $controllerObject = $controller;
        } else {
            return;
        }

        $request = $event->getRequest();

        // Call the initBeforeControllerAction method
        if (method_exists($controllerObject, 'initBeforeControllerAction')) {
            $controllerObject->initBeforeControllerAction($request);
        }

         // Call the _init method
        if (method_exists($controllerObject, '_init')) {
            $controllerObject->setRequest($request);
            $controllerObject->_init($request);
        }

        // Call the initializeRestrictedController method
        if (method_exists($controllerObject, 'initializeRestrictedController')) {
            $response = $controllerObject->initializeRestrictedController($request);
            if ($response) {
                $event->setController(function() use ($response) {
                    return $response;
                });
            }
        }

        // Call the initializePublicController method
        if (method_exists($controllerObject, 'initializePublicController')) {
            $response = $controllerObject->initializePublicController($request);
            if ($response) {
                $event->setController(function() use ($response) {
                    return $response;
                });
            }
        }
    }
}
